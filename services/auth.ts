import { Role } from "@prisma/client";

import { UserService } from "./user";
import { EmailService } from "./email";

import { prisma } from "@/lib/db";
import { hashPassword, verifyPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import { InvalidRolesError } from "@/lib/error-handler";

// Auth service exceptions
export class AuthenticationError extends Error {
  constructor(message: string = "Authentication failed") {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class EmailNotVerifiedError extends Error {
  constructor(message: string = "Email not verified") {
    super(message);
    this.name = "EmailNotVerifiedError";
  }
}

export class InvalidCredentialsError extends Error {
  constructor(message: string = "Invalid credentials") {
    super(message);
    this.name = "InvalidCredentialsError";
  }
}

export class PasswordMismatchError extends Error {
  constructor(message: string = "Current password is incorrect") {
    super(message);
    this.name = "PasswordMismatchError";
  }
}

export interface RegisterUserData {
  email: string;
  password: string;
  name?: string;
  roles: Role[];
}

/**
 * Auth Service - Business logic layer for authentication operations
 * Handles registration, login, password management, and role-based authentication flows
 */
export class AuthService {
  /**
   * Register a new user with email verification
   */
  static async registerUser(data: RegisterUserData): Promise<{
    message: string;
    user: {
      id: string;
      email: string;
      roles: Role[];
      emailVerified: Date | null;
      createdAt: Date;
    };
    emailSent: boolean;
    emailError?: string;
  }> {
    const { email, password, name, roles } = data;

    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      throw new AuthenticationError(
        "User with this email already exists, please sign in.",
      );
    }

    // Validate roles
    if (!roles || roles.length === 0) {
      throw new InvalidRolesError("At least one role is required");
    }

    if (roles.length > 2) {
      throw new InvalidRolesError("Maximum two roles allowed");
    }

    // Create user using UserService - now returns data directly
    const user = await UserService.createUser({
      email: sanitizedEmail,
      password,
      name,
      roles,
      emailVerified: false,
    });

    // Create verification token and send email
    const emailResult = await EmailService.createVerificationToken(
      user.id,
      user.email,
      user.name || undefined,
    );

    return {
      message:
        "Account created successfully! Please check your email to verify your account before signing in.",
      user: {
        id: user.id,
        email: user.email,
        roles: user.roles,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
      },
      emailError: undefined, // No error since EmailService now throws exceptions
    };
  }

  /**
   * Authenticate user with email and password
   */
  static async loginUser(email: string, password: string): Promise<AuthUser> {

    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Find user with password hash
    const user = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        passwordHash: true,
        roles: true,
        emailVerified: true,
      },
    });

    if (!user) {
      throw new InvalidCredentialsError("Invalid email or password");
    }

    // Check if user has a password (not OAuth-only user)
    if (!user.passwordHash) {
      throw new AuthenticationError(
        "This account was created with a social provider. Please sign in using that method.",
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);

    if (!isPasswordValid) {
      throw new InvalidCredentialsError("Invalid email or password");
    }

    // Check if email is verified
    if (!user.emailVerified) {
      const error = new EmailNotVerifiedError("EMAIL_NOT_VERIFIED");

      // Add additional data for the error handler
      (error as any).userId = user.id;
      (error as any).email = user.email;
      throw error;
    }

    // Return authenticated user
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.image,
      roles: user.roles,
      emailVerified: user.emailVerified,
    };
  }

  /**
   * Change user password
   */
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {

    // Get user with current password hash
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        passwordHash: true,
      },
    });

    if (!user) {
      throw new AuthenticationError("User not found");
    }

    if (!user.passwordHash) {
      throw new AuthenticationError(
        "This account doesn't have a password. It was created with a social provider.",
      );
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(
      currentPassword,
      user.passwordHash,
    );

    if (!isCurrentPasswordValid) {
      throw new PasswordMismatchError();
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash: newPasswordHash },
    });
  }

  /**
   * Assign roles to user (for onboarding)
   */
  static async assignUserRoles(
    userId: string,
    roles: Role[],
  ): Promise<{
    message: string;
    user: any;
  }> {
    // Validate roles
    if (!roles || roles.length === 0) {
      throw new InvalidRolesError("At least one role is required");
    }

    if (roles.length > 2) {
      throw new InvalidRolesError("Maximum two roles allowed");
    }

    // Use UserService to update user with roles - now returns data directly
    const user = await UserService.updateUser(userId, { roles });

    return {
      message: "Roles assigned successfully",
      user,
    };
  }

  /**
   * Get user authentication status and info
   */
  static async getUserAuthInfo(userId: string): Promise<{
    user: any;
    isEmailVerified: boolean;
    hasRoles: boolean;
    needsOnboarding: boolean;
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new AuthenticationError("User not found");
    }

    return {
      user,
      isEmailVerified: !!user.emailVerified,
      hasRoles: user.roles.length > 0,
      needsOnboarding: user.roles.length === 0,
    };
  }

  /**
   * Determine redirect path based on user roles and verification status
   */
  static getRedirectPath(user: AuthUser): string {
    // If email not verified, redirect to verification page
    if (!user.emailVerified) {
      return "/verify-email";
    }

    // If no roles assigned, redirect to onboarding
    if (user.roles.length === 0) {
      return "/onboarding";
    }

    // If user has only one role, redirect to role-specific dashboard
    if (user.roles.length === 1) {
      const role = user.roles[0];

      switch (role) {
        case Role.MODEL_PROVIDER:
          return "/dashboard/provider";
        case Role.ADVERTISER:
          return "/dashboard/advertiser";
        default:
          return "/dashboard";
      }
    }

    // If user has multiple roles, redirect to role selection dashboard
    return "/dashboard";
  }

  /**
   * Check if user has required role for access
   */
  static async checkUserAccess(
    userId: string,
    requiredRole: Role,
  ): Promise<{
    hasAccess: boolean;
    message: string;
  }> {
    // UserService.hasRole now returns boolean directly
    const hasRole = await UserService.hasRole(userId, requiredRole);

    return {
      hasAccess: hasRole,
      message: hasRole ? "Access granted" : "Access denied",
    };
  }

  /**
   * Verify email using token
   */
  static async verifyUserEmail(token: string): Promise<{
    message: string;
    user: any;
    welcomeEmailSent: boolean;
  }> {
    const result = await EmailService.verifyEmail(token);

    return {
      message:
        "Email verified successfully! You can now sign in to your account.",
      user: result.user,
      welcomeEmailSent: result.welcomeEmailSent,
    };
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(email: string): Promise<{
    message: string;
    emailSent: boolean;
  }> {
    const result = await EmailService.resendVerificationEmail(email);

    return {
      message: "Verification email sent successfully! Please check your inbox.",
      emailSent: result.emailSent,
    };
  }
}
