import { AppStatus } from "@prisma/client";
import { nanoid } from "nanoid";

import { prisma } from "@/lib/db";
import { getAppAnalytics as getAnalyticsData } from "@/lib/analytics";
import {
  UserNotFoundError,
  InsufficientPermissionsError,
} from "@/lib/error-handler";

// App service exceptions
export class AppNotFoundError extends Error {
  constructor(message: string = "Application not found") {
    super(message);
    this.name = "AppNotFoundError";
  }
}

export class InvalidAppCredentialsError extends Error {
  constructor(message: string = "Invalid app credentials") {
    super(message);
    this.name = "InvalidAppCredentialsError";
  }
}

export class AppNotActiveError extends Error {
  constructor(message: string = "Application is not active") {
    super(message);
    this.name = "AppNotActiveError";
  }
}

export interface CreateAppData {
  userId: string;
  name: string;
  description?: string;
}

export interface UpdateAppData {
  name?: string;
  description?: string;
  status?: AppStatus;
}

export interface AppWithAnalytics {
  id: string;
  name: string;
  appId: string;
  appSecret?: string;
  description: string | null;
  status: AppStatus;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  impressions: number;
  clicks: number;
  revenue: number;
  ctr: string;
}

export interface AppCredentials {
  appId: string;
  appSecret: string;
}

/**
 * App Service - Business logic layer for application management operations
 * Handles app creation, updates, credential management, and analytics
 */
export class AppService {
  /**
   * Create a new app for a user
   */
  static async createApp(data: CreateAppData): Promise<{
    message: string;
    app: AppWithAnalytics;
  }> {
    const { userId, name, description } = data;

    // Validate user exists and has MODEL_PROVIDER role
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, roles: true },
    });

    if (!user) {
      throw new UserNotFoundError();
    }

    if (!user.roles.includes("MODEL_PROVIDER")) {
      throw new InsufficientPermissionsError(
        "User must have MODEL_PROVIDER role to create apps",
      );
    }

    // Generate unique app ID and secret
    const appId = `app_${nanoid(16)}`;
    const appSecret = `secret_${nanoid(32)}`;

    // Create app
    const app = await prisma.app.create({
      data: {
        userId,
        name,
        appId,
        appSecret,
        description: description || null,
        status: AppStatus.ACTIVE,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      message: "App created successfully",
      app: {
        ...app,
        impressions: 0,
        clicks: 0,
        revenue: 0,
        ctr: "0.00",
      },
    };
  }

  /**
   * Get app by ID
   */
  static async getAppById(
    appId: string,
    includeSecret = false,
  ): Promise<AppWithAnalytics> {
    const selectFields: any = {
      id: true,
      name: true,
      appId: true,
      description: true,
      status: true,
      userId: true,
      createdAt: true,
      updatedAt: true,
    };

    if (includeSecret) {
      selectFields.appSecret = true;
    }

    const app = await prisma.app.findUnique({
      where: { id: appId },
      select: selectFields,
    });

    if (!app) {
      throw new AppNotFoundError();
    }

    // Get analytics data
    const analytics = await getAnalyticsData(app.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...app,
      appSecret: includeSecret ? app.appSecret : undefined,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return appWithAnalytics;
  }

  /**
   * Get app by appId (public identifier)
   */
  static async getAppByAppId(
    appId: string,
    includeSecret = false,
  ): Promise<AppWithAnalytics> {
    const selectFields: any = {
      id: true,
      name: true,
      appId: true,
      description: true,
      status: true,
      userId: true,
      createdAt: true,
      updatedAt: true,
    };

    if (includeSecret) {
      selectFields.appSecret = true;
    }

    const app = await prisma.app.findUnique({
      where: { appId },
      select: selectFields,
    });

    if (!app) {
      throw new AppNotFoundError();
    }

    // Get analytics data
    const analytics = await getAnalyticsData(app.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...app,
      appSecret: includeSecret ? app.appSecret : undefined,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return appWithAnalytics;
  }

  /**
   * Get all apps for a user
   */
  static async getUserApps(userId: string): Promise<AppWithAnalytics[]> {
    const apps = await prisma.app.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get analytics for each app
    const appsWithAnalytics = await Promise.all(
      apps.map(async (app) => {
        const analytics = await getAnalyticsData(app.id);

        return {
          ...app,
          impressions: analytics.impressions,
          clicks: analytics.clicks,
          revenue: analytics.revenue,
          ctr: analytics.ctr,
        };
      }),
    );

    return appsWithAnalytics;
  }

  /**
   * Update app information
   */
  static async updateApp(
    appId: string,
    data: UpdateAppData,
    userId?: string,
  ): Promise<{
    message: string;
    app: AppWithAnalytics;
  }> {
    const { name, description, status } = data;

    // Build where clause
    const whereClause: any = { id: appId };

    if (userId) {
      whereClause.userId = userId; // Ensure user can only update their own apps
    }

    // Prepare update data
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;

    const updatedApp = await prisma.app.update({
      where: whereClause,
      data: updateData,
      select: {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAnalyticsData(updatedApp.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...updatedApp,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return {
      message: "App updated successfully",
      app: appWithAnalytics,
    };
  }

  /**
   * Delete app
   */
  static async deleteApp(
    appId: string,
    userId?: string,
  ): Promise<{ message: string }> {
    // Build where clause
    const whereClause: any = { id: appId };

    if (userId) {
      whereClause.userId = userId; // Ensure user can only delete their own apps
    }

    await prisma.app.delete({
      where: whereClause,
    });

    return { message: "App deleted successfully" };
  }

  /**
   * Verify app credentials
   */
  static async verifyAppCredentials(
    appId: string,
    appSecret: string,
  ): Promise<{
    id: string;
    name: string;
    status: AppStatus;
    userId: string;
  }> {
    const app = await prisma.app.findUnique({
      where: { appId },
      select: {
        id: true,
        name: true,
        appSecret: true,
        status: true,
        userId: true,
      },
    });

    if (!app) {
      throw new InvalidAppCredentialsError();
    }

    if (app.appSecret !== appSecret) {
      throw new InvalidAppCredentialsError();
    }

    if (app.status !== AppStatus.ACTIVE) {
      throw new AppNotActiveError();
    }

    return {
      id: app.id,
      name: app.name,
      status: app.status,
      userId: app.userId,
    };
  }

  /**
   * Regenerate app secret
   */
  static async regenerateAppSecret(
    appId: string,
    userId?: string,
  ): Promise<{
    message: string;
    app: any;
    newSecret: string;
  }> {
    // Build where clause
    const whereClause: any = { id: appId };

    if (userId) {
      whereClause.userId = userId; // Ensure user can only regenerate their own app secrets
    }

    // Generate new secret
    const newAppSecret = `secret_${nanoid(32)}`;

    const updatedApp = await prisma.app.update({
      where: whereClause,
      data: { appSecret: newAppSecret },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      message: "App secret regenerated successfully",
      app: updatedApp,
      newSecret: newAppSecret,
    };
  }

  /**
   * Get app analytics
   */
  static async getAppAnalytics(
    appId: string,
    userId?: string,
  ): Promise<{
    appId: string;
    appName: string;
    analytics: any;
  }> {
    // Verify app exists and user has access
    const whereClause: any = { id: appId };

    if (userId) {
      whereClause.userId = userId;
    }

    const app = await prisma.app.findUnique({
      where: whereClause,
      select: { id: true, name: true },
    });

    if (!app) {
      throw new AppNotFoundError("App not found or access denied");
    }

    // Get analytics data
    const analytics = await getAnalyticsData(app.id);

    return {
      appId: app.id,
      appName: app.name,
      analytics,
    };
  }

  /**
   * Get app statistics for all apps
   */
  static async getAppStats(): Promise<{
    totalApps: number;
    activeApps: number;
    suspendedApps: number;
  }> {
    const [totalApps, activeApps, suspendedApps] = await Promise.all([
      prisma.app.count(),
      prisma.app.count({
        where: { status: AppStatus.ACTIVE },
      }),
      prisma.app.count({
        where: { status: AppStatus.SUSPENDED },
      }),
    ]);

    return {
      totalApps,
      activeApps,
      suspendedApps,
    };
  }
}
