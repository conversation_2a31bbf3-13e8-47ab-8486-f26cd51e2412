import { prisma } from "@/lib/db";
import {
  sendEmail,
  sendVerificationEmail,
  sendWelcomeEmail,
  generateVerificationToken,
  getVerificationExpiry,
  EmailData,
} from "@/lib/email";
import { UserNotFoundError } from "@/lib/error-handler";

// Custom email service exceptions
export class InvalidVerificationTokenError extends Error {
  constructor(message: string = "Invalid or expired verification token") {
    super(message);
    this.name = "InvalidVerificationTokenError";
  }
}

export class EmailAlreadyVerifiedError extends Error {
  constructor(message: string = "Email is already verified") {
    super(message);
    this.name = "EmailAlreadyVerifiedError";
  }
}

export class EmailSendError extends Error {
  constructor(message: string = "Failed to send email") {
    super(message);
    this.name = "EmailSendError";
  }
}

/**
 * Email Service - Business logic layer for email operations
 * Handles email verification, welcome emails, and general email sending
 */
export class EmailService {
  /**
   * Send a generic email
   */
  static async sendGenericEmail(emailData: EmailData): Promise<{
    provider: string;
  }> {
    const result = await sendEmail(emailData);

    if (!result.success) {
      throw new EmailSendError(result.error || "Failed to send email");
    }

    return { provider: result.provider || "unknown" };
  }

  /**
   * Create and store email verification token for a user
   */
  static async createVerificationToken(
    userId: string,
    email: string,
    name?: string,
  ): Promise<{
    userId: string;
    token: string;
    expires: Date;
  }> {
    // Generate verification token and expiry
    const verificationToken = generateVerificationToken();
    const verificationExpires = getVerificationExpiry();

    // Update user with verification token
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires,
      },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerificationToken: true,
        emailVerificationExpires: true,
      },
    });

    // Send verification email
    const emailResult = await sendVerificationEmail({
      email,
      token: verificationToken,
      name: name || updatedUser.name || undefined,
    });

    if (!emailResult.success) {
      throw new EmailSendError(
        emailResult.error || "Failed to send verification email",
      );
    }

    return {
      userId: updatedUser.id,
      token: verificationToken,
      expires: verificationExpires,
    };
  }

  /**
   * Verify email using token
   */
  static async verifyEmail(token: string): Promise<{
    user: {
      id: string;
      email: string;
      name: string | null;
      emailVerified: Date | null;
    };
    welcomeEmailSent: boolean;
  }> {
    // Find user with matching token
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpires: {
          gt: new Date(),
        },
      },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true,
      },
    });

    if (!user) {
      throw new InvalidVerificationTokenError();
    }

    if (user.emailVerified) {
      throw new EmailAlreadyVerifiedError();
    }

    // Update user as verified and clear verification fields
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
        emailVerificationToken: null,
        emailVerificationExpires: null,
      },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true,
      },
    });

    // Send welcome email
    const welcomeEmailResult = await sendWelcomeEmail(
      updatedUser.email,
      updatedUser.name || undefined,
    );

    return {
      user: updatedUser,
      welcomeEmailSent: welcomeEmailResult.success,
    };
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(email: string): Promise<{
    userId: string;
    token: string;
    expires: Date;
  }> {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true,
      },
    });

    if (!user) {
      throw new UserNotFoundError();
    }

    if (user.emailVerified) {
      throw new EmailAlreadyVerifiedError();
    }

    // Create new verification token
    return await this.createVerificationToken(
      user.id,
      user.email,
      user.name || undefined,
    );
  }

  /**
   * Send welcome email to user
   */
  static async sendWelcomeEmailToUser(
    email: string,
    name?: string,
  ): Promise<void> {
    const result = await sendWelcomeEmail(email, name);

    if (!result.success) {
      throw new EmailSendError(result.error || "Failed to send welcome email");
    }
  }

  /**
   * Check if user's email is verified
   */
  static async isEmailVerified(userId: string): Promise<{
    isVerified: boolean;
    verifiedAt: Date | null;
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        emailVerified: true,
      },
    });

    if (!user) {
      throw new UserNotFoundError();
    }

    return {
      isVerified: !!user.emailVerified,
      verifiedAt: user.emailVerified,
    };
  }

  /**
   * Get verification token info for a user
   */
  static async getVerificationTokenInfo(userId: string): Promise<{
    hasToken: boolean;
    expires: Date | null;
    isExpired: boolean;
    isVerified: boolean;
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        emailVerificationToken: true,
        emailVerificationExpires: true,
        emailVerified: true,
      },
    });

    if (!user) {
      throw new UserNotFoundError();
    }

    return {
      hasToken: !!user.emailVerificationToken,
      expires: user.emailVerificationExpires,
      isExpired: user.emailVerificationExpires
        ? new Date() > user.emailVerificationExpires
        : false,
      isVerified: !!user.emailVerified,
    };
  }
}
