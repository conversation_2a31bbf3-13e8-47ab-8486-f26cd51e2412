import { describe, it, expect, vi, beforeEach } from "vitest";
import { Role } from "@prisma/client";

import {
  UserService,
  UserNotFoundError,
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} from "./user";

import { prisma } from "@/lib/db";
import * as auth from "@/lib/auth";
import * as validation from "@/lib/validation";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      findFirst: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
  },
}));

vi.mock("@/lib/auth", () => ({
  hashPassword: vi.fn(),
}));

vi.mock("@/lib/validation", () => ({
  sanitizeEmail: vi.fn(),
}));

describe("UserService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(validation.sanitizeEmail).mockImplementation((email) =>
      email.toLowerCase(),
    );
  });

  describe("createUser", () => {
    it("should create user successfully", async () => {
      const mockUserData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER],
      };

      const mockCreatedUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(auth.hashPassword).mockResolvedValue("hashed-password");
      vi.mocked(prisma.user.create).mockResolvedValue(mockCreatedUser as any);

      const result = await UserService.createUser(mockUserData);

      expect(result).toEqual(mockCreatedUser);
      expect(auth.hashPassword).toHaveBeenCalledWith("password123");
    });

    it("should throw UserAlreadyExistsError when user exists", async () => {
      const mockUserData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User",
        roles: [Role.MODEL_PROVIDER],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        id: "existing",
      } as any);

      await expect(UserService.createUser(mockUserData)).rejects.toThrow(
        UserAlreadyExistsError,
      );
    });

    it("should create user without password", async () => {
      const mockUserData = {
        email: "<EMAIL>",
        name: "Test User",
        roles: [Role.ADVERTISER],
        emailVerified: true,
      };

      const mockCreatedUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.ADVERTISER],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);
      vi.mocked(prisma.user.create).mockResolvedValue(mockCreatedUser as any);

      const result = await UserService.createUser(mockUserData);

      expect(result).toEqual(mockCreatedUser);
      expect(auth.hashPassword).not.toHaveBeenCalled();
    });
  });

  describe("getUserById", () => {
    it("should get user successfully", async () => {
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await UserService.getUserById("user123");

      expect(result).toEqual(mockUser);
    });

    it("should throw UserNotFoundError when user not found", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      await expect(UserService.getUserById("nonexistent")).rejects.toThrow(
        UserNotFoundError,
      );
    });
  });

  describe("getUserByEmail", () => {
    it("should get user by email successfully", async () => {
      const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await UserService.getUserByEmail("<EMAIL>");

      expect(result).toEqual(mockUser);
      expect(validation.sanitizeEmail).toHaveBeenCalledWith("<EMAIL>");
    });
  });

  describe("updateUser", () => {
    it("should update user successfully", async () => {
      const mockUpdatedUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Updated Name",
        image: "new-image.jpg",
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);

      const result = await UserService.updateUser("user123", {
        name: "Updated Name",
        image: "new-image.jpg",
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      });

      expect(result).toEqual(mockUpdatedUser);
    });

    it("should throw EmailAlreadyTakenError when email is taken", async () => {
      vi.mocked(prisma.user.findFirst).mockResolvedValue({
        id: "other-user",
      } as any);

      await expect(
        UserService.updateUser("user123", {
          email: "<EMAIL>",
        }),
      ).rejects.toThrow(EmailAlreadyTakenError);
    });

    it("should update email successfully", async () => {
      const mockUpdatedUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER],
        emailVerified: null, // Reset when email changes
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.findFirst).mockResolvedValue(null); // No conflict
      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);

      const result = await UserService.updateUser("user123", {
        email: "<EMAIL>",
      });

      expect(result).toEqual(mockUpdatedUser);
      expect(result.emailVerified).toBeNull();
    });
  });

  describe("updateUser with roles", () => {
    it("should update user with roles successfully", async () => {
      const mockUpdatedUser = {
        id: "user123",
        email: "<EMAIL>",
        name: "Test User",
        image: null,
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(prisma.user.update).mockResolvedValue(mockUpdatedUser as any);

      const result = await UserService.updateUser("user123", {
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      });

      expect(result).toEqual(mockUpdatedUser);
      expect(result.roles).toEqual([Role.MODEL_PROVIDER, Role.ADVERTISER]);
    });

    it("should throw InvalidRolesError for empty roles", async () => {
      await expect(
        UserService.updateUser("user123", { roles: [] }),
      ).rejects.toThrow(InvalidRolesError);
    });

    it("should throw InvalidRolesError for too many roles", async () => {
      await expect(
        UserService.updateUser("user123", {
          roles: [Role.MODEL_PROVIDER, Role.ADVERTISER, Role.MODEL_PROVIDER],
        }),
      ).rejects.toThrow(InvalidRolesError);
    });
  });

  describe("hasRole", () => {
    it("should check role successfully", async () => {
      const mockUser = {
        roles: [Role.MODEL_PROVIDER],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await UserService.hasRole("user123", Role.MODEL_PROVIDER);

      expect(result).toBe(true);
    });

    it("should return false for missing role", async () => {
      const mockUser = {
        roles: [Role.MODEL_PROVIDER],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const result = await UserService.hasRole("user123", Role.ADVERTISER);

      expect(result).toBe(false);
    });

    it("should throw UserNotFoundError when user not found", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      await expect(
        UserService.hasRole("nonexistent", Role.MODEL_PROVIDER),
      ).rejects.toThrow(UserNotFoundError);
    });
  });

  describe("getUsersByRole", () => {
    it("should get users by role successfully", async () => {
      const mockUsers = [
        {
          id: "user1",
          email: "<EMAIL>",
          name: "User 1",
          image: null,
          roles: [Role.MODEL_PROVIDER],
          emailVerified: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "user2",
          email: "<EMAIL>",
          name: "User 2",
          image: null,
          roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
          emailVerified: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(prisma.user.findMany).mockResolvedValue(mockUsers as any);

      const result = await UserService.getUsersByRole(Role.MODEL_PROVIDER);

      expect(result).toEqual(mockUsers);
    });
  });

  describe("getUserStats", () => {
    it("should get user statistics successfully", async () => {
      vi.mocked(prisma.user.count)
        .mockResolvedValueOnce(100) // total users
        .mockResolvedValueOnce(80) // verified users
        .mockResolvedValueOnce(60) // model providers
        .mockResolvedValueOnce(40); // advertisers

      const result = await UserService.getUserStats();

      expect(result).toEqual({
        totalUsers: 100,
        verifiedUsers: 80,
        unverifiedUsers: 20,
        modelProviders: 60,
        advertisers: 40,
      });
    });
  });

  describe("deleteUser", () => {
    it("should delete user successfully", async () => {
      vi.mocked(prisma.user.delete).mockResolvedValue({} as any);

      await expect(UserService.deleteUser("user123")).resolves.toBeUndefined();
    });

    it("should throw UserNotFoundError when user not found", async () => {
      vi.mocked(prisma.user.delete).mockRejectedValue({
        code: "P2025",
        message: "Record to delete does not exist.",
      });

      await expect(UserService.deleteUser("nonexistent")).rejects.toThrow(
        UserNotFoundError,
      );
    });

    it("should propagate other delete errors", async () => {
      vi.mocked(prisma.user.delete).mockRejectedValue(
        new Error("Database connection failed"),
      );

      await expect(UserService.deleteUser("user123")).rejects.toThrow(
        "Database connection failed",
      );
    });
  });
});
