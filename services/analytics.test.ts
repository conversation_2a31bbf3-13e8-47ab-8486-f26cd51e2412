import { describe, it, expect, vi, beforeEach } from "vitest";
import { Role } from "@prisma/client";

import { AnalyticsService, InvalidRoleError } from "./analytics";

import { prisma } from "@/lib/db";
import * as analytics from "@/lib/analytics";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  prisma: {
    app: {
      findMany: vi.fn(),
    },
    advertisement: {
      findMany: vi.fn(),
    },
  },
}));

vi.mock("@/lib/analytics", () => ({
  getUserAppAnalytics: vi.fn(),
  getUserAdAnalytics: vi.fn(),
  getMonthlyAppAnalytics: vi.fn(),
  getMonthlyAdAnalytics: vi.fn(),
}));

describe("AnalyticsService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getRoleAnalytics", () => {
    it("should throw InvalidRoleError for invalid role", async () => {
      await expect(
        AnalyticsService.getRoleAnalytics({
          userId: "user-123",
          role: "invalid" as any,
          userRoles: [Role.MODEL_PROVIDER],
        }),
      ).rejects.toThrow(InvalidRoleError);
    });

    it("should return error when user lacks required role for model analytics", async () => {
      const result = await AnalyticsService.getRoleAnalytics({
        userId: "user-123",
        role: "model",
        userRoles: [Role.ADVERTISER],
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe(
        "User does not have required role: MODEL_PROVIDER",
      );
    });

    it("should return error when user lacks required role for advertiser analytics", async () => {
      const result = await AnalyticsService.getRoleAnalytics({
        userId: "user-123",
        role: "advertiser",
        userRoles: [Role.MODEL_PROVIDER],
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("User does not have required role: ADVERTISER");
    });

    it("should call getModelAnalytics for model role", async () => {
      const mockApps = [{ id: "app-1", name: "App 1", createdAt: new Date() }];
      const mockUserAnalytics = {
        totalImpressions: 100,
        totalClicks: 10,
        totalRevenue: 50,
        apps: [
          {
            id: "app-1",
            name: "App 1",
            impressions: 100,
            clicks: 10,
            revenue: 50,
          },
        ],
      };
      const mockMonthlyData = [
        { month: "Jan", impressions: 100, clicks: 10, revenue: 50 },
      ];

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);
      vi.mocked(analytics.getUserAppAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAppAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getRoleAnalytics({
        userId: "user-123",
        role: "model",
        userRoles: [Role.MODEL_PROVIDER],
      });

      expect(result.success).toBe(true);
      expect(result.data.analytics.totalApps).toBe(1);
      expect(result.data.analytics.totalImpressions).toBe(100);
      expect(result.data.analytics.totalClicks).toBe(10);
      expect(result.data.analytics.totalRevenue).toBe(50);
      expect(result.data.analytics.topApps).toHaveLength(1);
    });

    it("should call getAdvertiserAnalytics for advertiser role", async () => {
      const mockAds = [
        {
          id: "ad-1",
          name: "Ad 1",
          budget: "100",
          bidType: "CPC",
          bidAmount: "0.50",
          createdAt: new Date(),
        },
      ];
      const mockUserAnalytics = {
        totalImpressions: 200,
        totalClicks: 20,
        totalSpend: 10,
        averageCTR: "10.00",
        ads: [
          {
            id: "ad-1",
            name: "Ad 1",
            impressions: 200,
            clicks: 20,
            spend: 10,
            ctr: "10.00",
          },
        ],
      };
      const mockMonthlyData = [
        { month: "Jan", impressions: 200, clicks: 20, spend: 10 },
      ];

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(
        mockAds as any,
      );
      vi.mocked(analytics.getUserAdAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAdAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getRoleAnalytics({
        userId: "user-123",
        role: "advertiser",
        userRoles: [Role.ADVERTISER],
      });

      expect(result.success).toBe(true);
      expect(result.data.analytics.totalCampaigns).toBe(1);
      expect(result.data.analytics.totalBudget).toBe(100);
      expect(result.data.analytics.totalSpend).toBe(10);
      expect(result.data.analytics.totalImpressions).toBe(200);
      expect(result.data.analytics.totalClicks).toBe(20);
      expect(result.data.analytics.averageCTR).toBe("10.00");
      expect(result.data.analytics.topCampaigns).toHaveLength(1);
    });
  });

  describe("getModelAnalytics", () => {
    it("should return model analytics successfully", async () => {
      const mockApps = [
        { id: "app-1", name: "App 1", createdAt: new Date() },
        { id: "app-2", name: "App 2", createdAt: new Date() },
      ];
      const mockUserAnalytics = {
        totalImpressions: 300,
        totalClicks: 30,
        totalRevenue: 150,
        apps: [
          {
            id: "app-1",
            name: "App 1",
            impressions: 200,
            clicks: 20,
            revenue: 100,
          },
          {
            id: "app-2",
            name: "App 2",
            impressions: 100,
            clicks: 10,
            revenue: 50,
          },
        ],
      };
      const mockMonthlyData = [
        { month: "Jan", impressions: 300, clicks: 30, revenue: 150 },
      ];

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);
      vi.mocked(analytics.getUserAppAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAppAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(result.success).toBe(true);
      expect(result.data.analytics.totalApps).toBe(2);
      expect(result.data.analytics.totalImpressions).toBe(300);
      expect(result.data.analytics.totalClicks).toBe(30);
      expect(result.data.analytics.totalRevenue).toBe(150);
      expect(result.data.analytics.monthlyData).toEqual(mockMonthlyData);
      expect(result.data.analytics.topApps).toHaveLength(2);

      expect(prisma.app.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: { id: true, name: true, createdAt: true },
      });
    });

    it("should handle error when app not found in analytics", async () => {
      const mockApps = [{ id: "app-1", name: "App 1", createdAt: new Date() }];
      const mockUserAnalytics = {
        totalImpressions: 100,
        totalClicks: 10,
        totalRevenue: 50,
        apps: [
          {
            id: "app-missing",
            name: "Missing App",
            impressions: 100,
            clicks: 10,
            revenue: 50,
          },
        ],
      };
      const mockMonthlyData = [];

      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps as any);
      vi.mocked(analytics.getUserAppAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAppAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(result.success).toBe(false);
      expect(result.error).toBe("App with id app-missing not found");
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.app.findMany).mockRejectedValue(
        new Error("Database error"),
      );

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
    });
  });

  describe("getAdvertiserAnalytics", () => {
    it("should return advertiser analytics successfully", async () => {
      const mockAds = [
        {
          id: "ad-1",
          name: "Ad 1",
          budget: "100",
          bidType: "CPC",
          bidAmount: "0.50",
          createdAt: new Date(),
        },
        {
          id: "ad-2",
          name: "Ad 2",
          budget: "200",
          bidType: "CPM",
          bidAmount: "2.00",
          createdAt: new Date(),
        },
      ];
      const mockUserAnalytics = {
        totalImpressions: 500,
        totalClicks: 50,
        totalSpend: 25,
        averageCTR: "10.00",
        ads: [
          {
            id: "ad-1",
            name: "Ad 1",
            impressions: 300,
            clicks: 30,
            spend: 15,
            ctr: "10.00",
          },
          {
            id: "ad-2",
            name: "Ad 2",
            impressions: 200,
            clicks: 20,
            spend: 10,
            ctr: "10.00",
          },
        ],
      };
      const mockMonthlyData = [
        { month: "Jan", impressions: 500, clicks: 50, spend: 25 },
      ];

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(
        mockAds as any,
      );
      vi.mocked(analytics.getUserAdAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAdAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(result.success).toBe(true);
      expect(result.data.analytics.totalCampaigns).toBe(2);
      expect(result.data.analytics.totalBudget).toBe(300);
      expect(result.data.analytics.totalSpend).toBe(25);
      expect(result.data.analytics.totalImpressions).toBe(500);
      expect(result.data.analytics.totalClicks).toBe(50);
      expect(result.data.analytics.averageCTR).toBe("10.00");
      expect(result.data.analytics.monthlyData).toEqual(mockMonthlyData);
      expect(result.data.analytics.topCampaigns).toHaveLength(2);

      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: {
          id: true,
          name: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          createdAt: true,
        },
      });
    });

    it("should handle error when advertisement not found in analytics", async () => {
      const mockAds = [
        {
          id: "ad-1",
          name: "Ad 1",
          budget: "100",
          bidType: "CPC",
          bidAmount: "0.50",
          createdAt: new Date(),
        },
      ];
      const mockUserAnalytics = {
        totalImpressions: 100,
        totalClicks: 10,
        totalSpend: 5,
        averageCTR: "10.00",
        ads: [
          {
            id: "ad-missing",
            name: "Missing Ad",
            impressions: 100,
            clicks: 10,
            spend: 5,
            ctr: "10.00",
          },
        ],
      };
      const mockMonthlyData = [];

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(
        mockAds as any,
      );
      vi.mocked(analytics.getUserAdAnalytics).mockResolvedValue(
        mockUserAnalytics as any,
      );
      vi.mocked(analytics.getMonthlyAdAnalytics).mockResolvedValue(
        mockMonthlyData as any,
      );

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Advertisement with id ad-missing not found");
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.advertisement.findMany).mockRejectedValue(
        new Error("Database error"),
      );

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
    });
  });
});
