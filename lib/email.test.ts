import { describe, it, expect, vi, beforeEach } from "vitest";

// Create mock functions using vi.hoisted
const mockResendSend = vi.hoisted(() => vi.fn());
const mockGmailSendMail = vi.hoisted(() => vi.fn());

// Mock external dependencies
vi.mock("resend", () => ({
  Resend: vi.fn().mockImplementation(() => ({
    emails: {
      send: mockResendSend,
    },
  })),
}));

vi.mock("nodemailer", () => ({
  default: {
    createTransport: vi.fn().mockReturnValue({
      sendMail: mockGmailSendMail,
    }),
  },
}));

vi.mock("crypto", () => ({
  default: {
    randomBytes: vi.fn().mockReturnValue({
      toString: vi.fn().mockReturnValue("mock-token-12345"),
    }),
  },
}));

import {
  sendEmail,
  generateVerificationToken,
  getVerificationExpiry,
  type EmailData,
} from "@/lib/email";

import {
  getVerificationUrl,
  getEmailVerificationTemplate,
  getWelcomeEmailTemplate,
  type EmailVerificationData,
} from "@/lib/email-templates";

import { EmailService } from "@/services/email";

describe("email utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockResendSend.mockClear();
    mockGmailSendMail.mockClear();

    // Reset environment variables
    process.env.RESEND_API_KEY = "test-resend-key";
    process.env.GMAIL_USER = "<EMAIL>";
    process.env.GMAIL_PASS = "test-password";
    process.env.EMAIL_FROM = "<EMAIL>";
    process.env.NEXT_PUBLIC_APP_URL = "http://localhost:3000";
  });

  describe("generateVerificationToken", () => {
    it("should generate a verification token", () => {
      const token = generateVerificationToken();

      expect(token).toBe("mock-token-12345");
    });
  });

  describe("getVerificationExpiry", () => {
    it("should return a date 24 hours from now", () => {
      const now = new Date();
      const expiry = getVerificationExpiry();
      const expectedExpiry = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      // Allow for small time differences (within 1 second)
      expect(
        Math.abs(expiry.getTime() - expectedExpiry.getTime()),
      ).toBeLessThan(1000);
    });
  });

  describe("getVerificationUrl", () => {
    it("should generate verification URL with token", () => {
      const token = "test-token-123";
      const url = getVerificationUrl(token);

      expect(url).toBe(
        "http://localhost:3000/verify-email?token=test-token-123",
      );
    });

    it("should use default URL when NEXT_PUBLIC_APP_URL is not set", () => {
      delete process.env.NEXT_PUBLIC_APP_URL;
      const token = "test-token-123";
      const url = getVerificationUrl(token);

      expect(url).toBe(
        "http://localhost:3000/verify-email?token=test-token-123",
      );
    });
  });

  describe("getEmailVerificationTemplate", () => {
    it("should generate email template with user data", () => {
      const data: EmailVerificationData = {
        email: "<EMAIL>",
        token: "test-token-123",
        name: "John Doe",
      };

      const template = getEmailVerificationTemplate(data);

      expect(template.subject).toBe(
        "Verify your email address - Mindify AiD Platform",
      );
      expect(template.html).toContain("Hi John Doe,");
      expect(template.html).toContain(
        "http://localhost:3000/verify-email?token=test-token-123",
      );
      expect(template.text).toContain("Hi John Doe,");
      expect(template.text).toContain(
        "http://localhost:3000/verify-email?token=test-token-123",
      );
    });

    it("should use default name when not provided", () => {
      const data: EmailVerificationData = {
        email: "<EMAIL>",
        token: "test-token-123",
      };

      const template = getEmailVerificationTemplate(data);

      expect(template.html).toContain("Hi there,");
      expect(template.text).toContain("Hi there,");
    });
  });

  describe("getWelcomeEmailTemplate", () => {
    it("should generate welcome email template with name", () => {
      const template = getWelcomeEmailTemplate("Jane Doe");

      expect(template.subject).toBe("Welcome to Mindify AiD Platform!");
      expect(template.html).toContain("Hi Jane Doe,");
      expect(template.html).toContain("http://localhost:3000/dashboard");
      expect(template.text).toContain("Hi Jane Doe,");
      expect(template.text).toContain("http://localhost:3000/dashboard");
    });

    it("should use default name when not provided", () => {
      const template = getWelcomeEmailTemplate();

      expect(template.html).toContain("Hi there,");
      expect(template.text).toContain("Hi there,");
    });
  });

  describe("sendEmail", () => {
    it("should send email via Resend when configured", async () => {
      mockResendSend.mockResolvedValue({ id: "test-id" });

      const emailData: EmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      };

      const result = await sendEmail(emailData);

      expect(mockResendSend).toHaveBeenCalledWith({
        from: "<EMAIL>",
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      });
      expect(result).toEqual({ success: true, provider: "resend" });
    });

    it("should fallback to Gmail when Resend fails", async () => {
      mockResendSend.mockResolvedValue({ error: "Test error" });
      mockGmailSendMail.mockResolvedValue({ messageId: "test-message-id" });

      const emailData: EmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      };

      const result = await sendEmail(emailData);

      expect(mockGmailSendMail).toHaveBeenCalledWith({
        from: "<EMAIL>",
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      });
      expect(result).toEqual({ success: true, provider: "gmail" });
    });

    it("should return error when no email service is configured", async () => {
      delete process.env.RESEND_API_KEY;
      delete process.env.GMAIL_USER;
      delete process.env.GMAIL_PASS;

      const emailData: EmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
      };

      const result = await sendEmail(emailData);

      expect(result).toEqual({
        success: false,
        error: "No email service configured",
        provider: "gmail",
      });
    });

    it("should use custom from email when provided", async () => {
      mockResendSend.mockResolvedValue({ id: "test-id" });

      const emailData: EmailData = {
        to: "<EMAIL>",
        subject: "Test Subject",
        html: "<p>Test HTML</p>",
        text: "Test text",
        from: "<EMAIL>",
      };

      await sendEmail(emailData);

      expect(mockResendSend).toHaveBeenCalledWith(
        expect.objectContaining({
          from: "<EMAIL>",
        }),
      );
    });
  });

  describe("sendVerificationEmail", () => {
    it("should send verification email using sendEmail function", async () => {
      mockResendSend.mockResolvedValue({ id: "test-id" });

      const data: EmailVerificationData = {
        email: "<EMAIL>",
        token: "test-token-123",
        name: "John Doe",
      };

      const result = await EmailService.sendVerificationEmail(data);

      expect(mockResendSend).toHaveBeenCalledWith(
        expect.objectContaining({
          to: "<EMAIL>",
          subject: "Verify your email address - Mindify AiD Platform",
          html: expect.stringContaining("Hi John Doe,"),
          text: expect.stringContaining("Hi John Doe,"),
        }),
      );
      expect(result).toEqual({ success: true });
    });

    it("should handle email sending errors", async () => {
      delete process.env.RESEND_API_KEY;
      delete process.env.GMAIL_USER;
      delete process.env.GMAIL_PASS;

      const data: EmailVerificationData = {
        email: "<EMAIL>",
        token: "test-token-123",
        name: "John Doe",
      };

      const result = await EmailService.sendVerificationEmail(data);

      expect(result).toEqual({
        success: false,
        error: "No email service configured",
      });
    });
  });

  describe("sendWelcomeEmail", () => {
    it("should send welcome email using sendEmail function", async () => {
      mockResendSend.mockResolvedValue({ id: "test-id" });

      const result = await EmailService.sendWelcomeEmail(
        "<EMAIL>",
        "Jane Doe",
      );

      expect(mockResendSend).toHaveBeenCalledWith(
        expect.objectContaining({
          to: "<EMAIL>",
          subject: "Welcome to Mindify AiD Platform!",
          html: expect.stringContaining("Hi Jane Doe,"),
          text: expect.stringContaining("Hi Jane Doe,"),
        }),
      );
      expect(result).toEqual({ success: true });
    });

    it("should handle email sending errors", async () => {
      delete process.env.RESEND_API_KEY;
      delete process.env.GMAIL_USER;
      delete process.env.GMAIL_PASS;

      const result = await EmailService.sendWelcomeEmail(
        "<EMAIL>",
        "Jane Doe",
      );

      expect(result).toEqual({
        success: false,
        error: "No email service configured",
      });
    });

    it("should use default name when not provided", async () => {
      mockResendSend.mockResolvedValue({ id: "test-id" });

      await EmailService.sendWelcomeEmail("<EMAIL>");

      expect(mockResendSend).toHaveBeenCalledWith(
        expect.objectContaining({
          html: expect.stringContaining("Hi there,"),
          text: expect.stringContaining("Hi there,"),
        }),
      );
    });
  });
});
